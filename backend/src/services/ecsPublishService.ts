import { ECSClient, CreateServiceCommand, DescribeServicesCommand, ListTasksCommand, DescribeTasksCommand, RegisterTaskDefinitionCommand } from '@aws-sdk/client-ecs';
import { EC2Client, DescribeNetworkInterfacesCommand } from '@aws-sdk/client-ec2';
import { getAWSConfig, getDefaultServiceConfig, getDefaultTaskDefinition } from '../config/aws';
import { getAppConfig } from '../config/app';
import { Route53LiveDNSService } from './route53Service';
import { S3FileService } from './s3FileService';
import axios from 'axios';

export interface PublishResult {
  success: boolean;
  liveUrl?: string;
  hostname?: string;
  publicIp?: string;
  message: string;
  error?: string;
  alreadyRunning?: boolean;
}

export class ECSPublishService {
  private ecsClient: ECSClient;
  private ec2Client: EC2Client;
  private config: ReturnType<typeof getAWSConfig>;

  constructor() {
    this.config = getAWSConfig();
    this.ecsClient = new ECSClient({
      region: this.config.region,
      credentials: {
        accessKeyId: this.config.accessKeyId,
        secretAccessKey: this.config.secretAccessKey
      }
    });
    this.ec2Client = new EC2Client({
      region: this.config.region,
      credentials: {
        accessKeyId: this.config.accessKeyId,
        secretAccessKey: this.config.secretAccessKey
      }
    });
  }

  async startPublishFromWorkspaceName(workspaceName: string, userId: string, userEmail: string, options?: { force?: boolean }): Promise<PublishResult> {
    try {
      const interviewUuid = workspaceName.replace('workspace_', '');
      if (!interviewUuid) {
        return { success: false, message: 'Invalid workspace name' };
      }

      // Short-name prefix with first 8 chars; actual service name will include a time suffix
      const short = interviewUuid.substring(0, 8);
      const prefix = `publish-${short}-`;
      const serviceName = `${prefix}${Date.now().toString().slice(-5)}`;



      // Find any existing publish services for this interview (by short-name prefix)
      const { ListServicesCommand } = await import('@aws-sdk/client-ecs');
      let nextToken: string | undefined = undefined;
      const matchedServiceNames: string[] = [];
      do {
        const listResp: any = await this.ecsClient.send(new ListServicesCommand({ cluster: this.config.ecsClusterName, nextToken }));
        nextToken = listResp.nextToken as string | undefined;
        const arns: string[] = (listResp.serviceArns || []) as string[];
        if (arns.length > 0) {
          const desc = await this.ecsClient.send(new DescribeServicesCommand({ cluster: this.config.ecsClusterName, services: arns }));
          for (const s of desc.services || []) {
            const sName = s.serviceName || '';
            if (s.status !== 'INACTIVE' && sName.startsWith(prefix)) {
              matchedServiceNames.push(sName);
            }
          }
        }
      } while (nextToken);

      const anyExistingName = matchedServiceNames[0];

      if (anyExistingName && !options?.force) {
        // Reuse existing publish if present
        const publicIp = await this.resolvePublicIp(anyExistingName);

        const dnsService = new Route53LiveDNSService();
        let liveHostname: string | undefined;
        if (dnsService.isEnabled() && publicIp) {
          const recordName = dnsService.getRecordName(interviewUuid);
          await dnsService.upsertARecord(recordName, publicIp);
          liveHostname = recordName;
        }

        const effectiveHost = liveHostname || publicIp;
        if (!effectiveHost) {
          return { success: false, message: 'Existing publish found but public IP is not available yet' };
        }
        const liveUrl = `http://${effectiveHost}:${this.config.previewPort}`;
        return { success: true, liveUrl, hostname: liveHostname, publicIp: publicIp, message: 'Existing publish is running', alreadyRunning: true };
      }

      if (anyExistingName && options?.force) {
        // Delete all matched services before recreating to avoid draining conflicts
        try {
          const { UpdateServiceCommand, DeleteServiceCommand } = await import('@aws-sdk/client-ecs');
          for (const sName of matchedServiceNames) {
            try {
              await this.ecsClient.send(new UpdateServiceCommand({ cluster: this.config.ecsClusterName, service: sName, desiredCount: 0 }));
            } catch {}
            try {
              await this.ecsClient.send(new DeleteServiceCommand({ cluster: this.config.ecsClusterName, service: sName, force: true }));
            } catch {}
          }
        } catch (e) {
          // continue
        }
      }

      // 1) Register a task definition for the publish container
      const taskDef = getDefaultTaskDefinition(this.config);
      if (taskDef.containerDefinitions && taskDef.containerDefinitions[0] && taskDef.containerDefinitions[0].environment) {
        taskDef.containerDefinitions[0].environment.push(
          { name: 'PUBLISH_FOR', value: interviewUuid },
          { name: 'PUBLISH_MODE', value: 'true' },
          { name: 'USER_ID', value: userId }
        );
      }

      const reg = await this.ecsClient.send(new RegisterTaskDefinitionCommand(taskDef));
      const taskDefinitionArn = reg.taskDefinition?.taskDefinitionArn;
      if (!taskDefinitionArn) {
        return { success: false, message: 'Failed to register task definition for publish' };
      }

      // 2) Create an ECS service with short name + time suffix
      const serviceConfig = getDefaultServiceConfig(this.config, taskDefinitionArn, serviceName);
      const createResp = await this.ecsClient.send(new CreateServiceCommand(serviceConfig));
      const serviceArn = createResp.service?.serviceArn;
      if (!serviceArn) {
        return { success: false, message: 'Failed to create publish ECS service' };
      }

      // 3) Wait for task to start and get public IP
      const sleep = (ms: number) => new Promise(r => setTimeout(r, ms));

      let publicIp: string | undefined;
      for (let i = 0; i < 18; i++) { // up to ~90s
        await sleep(5000);
        const ds = await this.ecsClient.send(new DescribeServicesCommand({ cluster: this.config.ecsClusterName, services: [serviceName] }));
        const running = ds.services?.[0]?.runningCount || 0;
        if (running > 0) {
          publicIp = await this.resolvePublicIp(serviceName);
          if (publicIp) break;
        }
      }

      if (!publicIp) {
        return { success: false, message: 'Publish container did not obtain a public IP in time' };
      }

      // 4) Create/ensure live DNS
      const dnsService = new Route53LiveDNSService();
      let liveHostname: string | undefined;
      if (dnsService.isEnabled()) {
        const recordName = dnsService.getRecordName(interviewUuid);
        await dnsService.upsertARecord(recordName, publicIp);
        liveHostname = recordName;
      }

      // 5) Pull files from S3 and upload into container
      const s3 = new S3FileService();
      const s3Files = await s3.downloadWorkspaceFiles(userEmail, interviewUuid);
      const files = s3Files.success && s3Files.data?.files ? s3Files.data.files : {};

      const containerUrl = `http://${publicIp}:${this.config.containerPort}`;
      for (const [path, content] of Object.entries(files)) {
        try {
          await axios.put(`${containerUrl}/files/${encodeURIComponent(path)}`, content, {
            headers: { 'Content-Type': 'text/plain' },
            timeout: 20000
          });
        } catch (e: any) {
          // Continue uploading other files
        }
      }

      // 6) Trigger publish inside container (npm ci -> build -> preview)
      try {
        const pubResp = await axios.post(`${containerUrl}/api/publish/start`, { port: this.config.previewPort }, { timeout: getAppConfig().publishStartTimeoutMs });
        if (!(pubResp.status === 200 && pubResp.data?.success) && pubResp.status !== 202) {
          return { success: false, publicIp, hostname: liveHostname, message: 'Failed to start publish inside container' };
        }
      } catch (err: any) {
        // Allow timeout response 202 as acceptable (server starting)
      }

      const effectiveHost = liveHostname || publicIp;
      const liveUrl = `http://${effectiveHost}:${this.config.previewPort}`;
      return { success: true, liveUrl, hostname: liveHostname, publicIp, message: 'Publish started successfully' };
    } catch (error: any) {
      return { success: false, message: 'Publish failed', error: error?.message || String(error) };
    }
  }

  /**
   * Helper to resolve public IP for a service's running task
   */
  private async resolvePublicIp(serviceName: string): Promise<string | undefined> {
    const lt = await this.ecsClient.send(new ListTasksCommand({ cluster: this.config.ecsClusterName, serviceName }));
    if (lt.taskArns && lt.taskArns.length > 0) {
      const taskArn = lt.taskArns[0]!;
      const dt = await this.ecsClient.send(new DescribeTasksCommand({ cluster: this.config.ecsClusterName, tasks: [taskArn] }));
      const task = dt.tasks && dt.tasks[0] ? dt.tasks[0] : undefined;
      if (task && task.attachments) {
        for (const attachment of task.attachments) {
          if (attachment.type === 'ElasticNetworkInterface' && attachment.details) {
            for (const detail of attachment.details) {
              if (detail.name === 'networkInterfaceId' && detail.value) {
                const eni = await this.ec2Client.send(new DescribeNetworkInterfacesCommand({ NetworkInterfaceIds: [detail.value] }));
                return eni.NetworkInterfaces?.[0]?.Association?.PublicIp;
              }
            }
          }
        }
      }
    }
    return undefined;
  }

  /**
   * Cleanup publish workspaces for a given interview UUID
   */
  async cleanupPublishWorkspaces(interviewUuid: string): Promise<{ servicesDeleted: number; dnsRecordsDeleted: number }> {
    const stats = { servicesDeleted: 0, dnsRecordsDeleted: 0 };

    try {
      // Get the short prefix for this interview
      const short = interviewUuid.substring(0, 8);
      const publishPrefix = `publish-${short}-`;

      // Import ECS client
      const { ECSClient, ListServicesCommand, DeleteServiceCommand, DescribeServicesCommand } = await import('@aws-sdk/client-ecs');
      const ecsClient = new ECSClient(this.config);

      // List all services to find publish services
      let nextToken: string | undefined;
      const publishServices: string[] = [];

      do {
        const listCommand = new ListServicesCommand({
          cluster: this.config.ecsClusterName,
          maxResults: 100,
          nextToken
        });

        const services = await ecsClient.send(listCommand);
        nextToken = services.nextToken;

        if (services.serviceArns) {
          for (const serviceArn of services.serviceArns) {
            const serviceName = serviceArn.split('/').pop() || '';
            if (serviceName.startsWith(publishPrefix)) {
              publishServices.push(serviceArn);
            }
          }
        }
      } while (nextToken);

      // Get service details to find public IPs for DNS cleanup
      const serviceIPs: { [serviceName: string]: string } = {};

      if (publishServices.length > 0) {
        try {
          const describeCommand = new DescribeServicesCommand({
            cluster: this.config.ecsClusterName,
            services: publishServices
          });

          const serviceDetails = await ecsClient.send(describeCommand);

          // For each service, try to get its public IP
          for (const service of serviceDetails.services || []) {
            if (service.serviceName) {
              try {
                const publicIp = await this.resolvePublicIp(service.serviceName);
                if (publicIp) {
                  serviceIPs[service.serviceName] = publicIp;
                }
              } catch (ipError) {
                console.warn(`Failed to resolve IP for service ${service.serviceName}:`, ipError);
              }
            }
          }
        } catch (describeError) {
          console.warn('Failed to describe services for IP resolution:', describeError);
        }
      }

      // Delete publish services
      for (const serviceArn of publishServices) {
        try {
          const deleteCommand = new DeleteServiceCommand({
            cluster: this.config.ecsClusterName,
            service: serviceArn,
            force: true
          });
          await ecsClient.send(deleteCommand);
          stats.servicesDeleted++;
        } catch (serviceError) {
          console.error(`Failed to delete publish service ${serviceArn}:`, serviceError);
        }
      }

      // Clean up live DNS records
      try {
        const liveDnsService = new Route53LiveDNSService();
        if (liveDnsService.isEnabled()) {
          const recordName = liveDnsService.getRecordName(interviewUuid);

          // Try to find an IP from the services we're deleting
          const availableIPs = Object.values(serviceIPs);
          if (availableIPs.length > 0) {
            // Use the first available IP for DNS cleanup
            await liveDnsService.deleteARecord(recordName, availableIPs[0]);
            stats.dnsRecordsDeleted++;
          } else {
            console.warn(`No IP available for DNS cleanup of ${recordName}`);
          }
        }
      } catch (dnsError) {
        console.warn('Failed to cleanup live DNS record:', dnsError);
      }

    } catch (error) {
      console.error('Failed to cleanup publish workspaces:', error);
    }

    return stats;
  }
}