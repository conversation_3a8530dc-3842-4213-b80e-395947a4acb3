import axios from 'axios';
import { Workspace } from '../models/Workspace';
import { ECSContainer } from '../models/ECSContainer';
import { getAppConfig } from '../config/app';

export interface PreviewInfo {
  previewUrl: string;
  status: 'starting' | 'running' | 'stopped' | 'error';
  message?: string;
}

export interface PreviewStartResult {
  success: boolean;
  previewUrl?: string;
  backendUrl?: string;
  message: string;
  error?: string;
}

export interface PublishStartResult {
  success: boolean;
  liveUrl?: string;
  message: string;
  error?: string;
}

export class ECSPreviewService {

  /**
   * Start preview server in ECS container
   */
  async startPreview(workspaceId: string): Promise<PreviewStartResult> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      if (!workspace) {
        return {
          success: false,
          message: 'Workspace not found'
        };
      }

      // Check if container is running
      if (workspace.containerStatus !== 'running') {
        return {
          success: false,
          message: `Container not ready - status: ${workspace.containerStatus}. Please wait for container to start.`
        };
      }

      if (!workspace.publicIp) {
        return {
          success: false,
          message: 'Container IP not available. Container may still be starting.'
        };
      }

      // Test container health before trying preview
      const containerUrl = `http://${workspace.publicIp}:${workspace.containerConfig.port}`;

      try {
        await axios.get(`${containerUrl}/health`, { timeout: getAppConfig().workspaceHealthCheckTimeoutMs });
      } catch (healthError: any) {
        return {
          success: false,
          message: `Container is not responding. Status: ${workspace.containerStatus}. Please wait a few minutes for container to fully start.`,
          error: 'container_not_ready'
        };
      }

      // Check if preview is already running
      if (workspace.previewUrl) {
        const isHealthy = await this.checkPreviewHealth(workspace.previewUrl);
        if (isHealthy) {
          return {
            success: true,
            previewUrl: workspace.previewUrl,
            message: 'Preview server is already running'
          };
        }
      }

      // Start preview server (reuse containerUrl from health check)
      
      try {
        const response = await axios.post(`${containerUrl}/api/preview/start`, {
          port: 8080,
          command: 'auto' // Auto-detect project type and start appropriate dev server
        }, {
          timeout: getAppConfig().previewStartTimeoutMs // Dedicated timeout for dev server startup
        });

        if (response.data && response.data.success) {
          const effectiveHost = workspace.previewHostname || workspace.publicIp;
          const previewUrl = `http://${effectiveHost}:8080`;
          const backendUrl = `http://${effectiveHost}:8000`;

          // Update workspace with preview URL
          workspace.previewUrl = previewUrl;
          await workspace.save();

          // Update container record
          const container = await ECSContainer.findOne({ workspaceId });
          if (container) {
            container.previewUrl = previewUrl;
            await container.addEvent('started', 'Preview services started (frontend + backend)');
          }

          return {
            success: true,
            previewUrl,
            backendUrl,
            message: 'Preview services started successfully (frontend + backend)'
          };
        } else if (response.status === 202) {
          // Handle timeout case - server is starting but not ready yet
          return {
            success: false,
            message: response.data?.message || 'Preview server is starting but taking longer than expected',
            error: 'timeout'
          };
        } else {
          return {
            success: false,
            message: response.data?.message || 'Failed to start preview server'
          };
        }
      } catch (containerError: any) {
        console.error('❌ Error communicating with container:', containerError);

        return {
          success: false,
          message: 'Failed to communicate with container',
          error: containerError.message
        };
      }

    } catch (error: any) {
      console.error('❌ Error starting preview:', error);
      return {
        success: false,
        message: 'Failed to start preview server',
        error: error.message
      };
    }
  }

  /**
   * Start publish flow (ci -> build -> preview) and return live URL
   */
  async startPublish(workspaceId: string): Promise<PublishStartResult> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      if (!workspace) {
        return { success: false, message: 'Workspace not found' };
      }

      if (workspace.containerStatus !== 'running') {
        return {
          success: false,
          message: `Container not ready - status: ${workspace.containerStatus}. Please wait for container to start.`
        };
      }

      if (!workspace.publicIp) {
        return { success: false, message: 'Container IP not available. Container may still be starting.' };
      }

      const containerUrl = `http://${workspace.publicIp}:${workspace.containerConfig.port}`;

      // Health check
      try {
        await axios.get(`${containerUrl}/health`, { timeout: getAppConfig().workspaceHealthCheckTimeoutMs });
      } catch (healthError: any) {
        return {
          success: false,
          message: `Container is not responding. Status: ${workspace.containerStatus}. Please wait a few minutes for container to fully start.`,
          error: 'container_not_ready'
        };
      }

      // Kick off publish pipeline inside container
      try {
        const response = await axios.post(`${containerUrl}/api/publish/start`, { port: 8080 }, { timeout: getAppConfig().publishStartTimeoutMs });
        if (response.status === 200 && response.data?.success) {
          // Create/ensure live DNS
          const { Route53LiveDNSService } = await import('./route53Service');
          const dnsService = new Route53LiveDNSService();
          let liveHostname: string | undefined = undefined;

          if (dnsService.isEnabled()) {
            const recordName = dnsService.getRecordName(workspace.interviewUuid);
            await dnsService.upsertARecord(recordName, workspace.publicIp);
            liveHostname = recordName;
          }

          const effectiveHost = liveHostname || workspace.publicIp;
          const liveUrl = `http://${effectiveHost}:8080`;

          // Update records
          workspace.previewUrl = liveUrl; // reuse field to store published URL as well
          await workspace.save();
          const container = await ECSContainer.findOne({ workspaceId });
          if (container) {
            container.previewUrl = liveUrl;
            await container.addEvent('started', 'Publish server started');
          }

          return { success: true, liveUrl, message: 'Published successfully' };
        } else if (response.status === 202) {
          return {
            success: false,
            message: response.data?.message || 'Publish is starting but taking longer than expected',
            error: 'timeout'
          };
        } else {
          return { success: false, message: response.data?.message || 'Failed to start publish' };
        }
      } catch (containerError: any) {
        return { success: false, message: 'Failed to start publish in container', error: containerError.message };
      }
    } catch (error: any) {
      return { success: false, message: 'Error starting publish', error: error.message };
    }
  }

  /**
   * Stop preview server in ECS container
   */
  async stopPreview(workspaceId: string): Promise<PreviewStartResult> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      if (!workspace) {
        return {
          success: false,
          message: 'Workspace not found'
        };
      }

      if (!workspace.publicIp) {
        return {
          success: false,
          message: 'Container IP not available'
        };
      }

      const containerUrl = `http://${workspace.publicIp}:${workspace.containerConfig.port}`;
      
      try {
        const response = await axios.post(`${containerUrl}/api/preview/stop`, {}, {
          timeout: getAppConfig().workspaceHealthCheckTimeoutMs
        });

        if (response.data && response.data.success) {
          // Clear preview URL
          workspace.previewUrl = undefined;
          await workspace.save();

          // Update container record
          const container = await ECSContainer.findOne({ workspaceId });
          if (container) {
            container.previewUrl = undefined;
            await container.addEvent('stopped', 'Preview server stopped');
          }

          return {
            success: true,
            message: 'Preview server stopped successfully'
          };
        } else {
          return {
            success: false,
            message: response.data?.message || 'Failed to stop preview server'
          };
        }
      } catch (containerError: any) {
        console.error('❌ Error communicating with container:', containerError);
        
        // Clear preview URL anyway
        workspace.previewUrl = undefined;
        await workspace.save();

        return {
          success: true,
          message: 'Preview server stopped (container may be unreachable)'
        };
      }

    } catch (error: any) {
      console.error('❌ Error stopping preview:', error);
      return {
        success: false,
        message: 'Failed to stop preview server',
        error: error.message
      };
    }
  }

  /**
   * Get preview status
   */
  async getPreviewStatus(workspaceId: string): Promise<PreviewInfo | null> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      if (!workspace) {
        return null;
      }

      if (workspace.containerStatus !== 'running') {
        return {
          previewUrl: '',
          status: 'stopped',
          message: `Container not running - status: ${workspace.containerStatus}`
        };
      }

      if (!workspace.previewUrl) {
        return {
          previewUrl: '',
          status: 'stopped',
          message: 'Preview server not started'
        };
      }

      // Check if preview is healthy
      const isHealthy = await this.checkPreviewHealth(workspace.previewUrl);
      
      return {
        previewUrl: workspace.previewUrl,
        status: isHealthy ? 'running' : 'error',
        message: isHealthy ? 'Preview server is running' : 'Preview server is not responding'
      };

    } catch (error: any) {
      console.error('❌ Error getting preview status:', error);
      return {
        previewUrl: '',
        status: 'error',
        message: 'Failed to get preview status'
      };
    }
  }

  /**
   * Check if preview server is healthy
   */
  async checkPreviewHealth(previewUrl: string): Promise<boolean> {
    try {
      await axios.get(previewUrl, {
        timeout: 5000,
        validateStatus: (status) => status < 500 // Accept any status < 500 as healthy
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check backend service status
   */
  async checkBackendStatus(workspaceId: string): Promise<{ success: boolean; status?: string; port?: number; isRunning?: boolean; message?: string; error?: string }> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      if (!workspace || !workspace.publicIp) {
        return {
          success: false,
          message: 'Workspace not available'
        };
      }

      const containerUrl = `http://${workspace.publicIp}:${workspace.containerConfig.port}`;

      try {
        const response = await axios.get(`${containerUrl}/api/backend/status`, {
          timeout: 5000
        });

        return {
          success: true,
          ...response.data
        };
      } catch (err: any) {
        return {
          success: false,
          message: 'Failed to get backend status',
          error: err.message
        };
      }
    } catch (err: any) {
      return {
        success: false,
        message: 'Error checking backend status',
        error: err.message
      };
    }
  }

  /**
   * Get preview logs
   */
  async getPreviewLogs(workspaceId: string): Promise<string[]> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      if (!workspace || !workspace.publicIp) {
        return [];
      }

      const containerUrl = `http://${workspace.publicIp}:${workspace.containerConfig.port}`;
      
      const response = await axios.get(`${containerUrl}/api/preview/logs`, {
        timeout: getAppConfig().workspaceHealthCheckTimeoutMs
      });

      if (response.data && response.data.logs) {
        return response.data.logs;
      }

      return [];
    } catch (error) {
      console.error('❌ Error getting preview logs:', error);
      return [];
    }
  }

  /**
   * Restart preview server
   */
  async restartPreview(workspaceId: string): Promise<PreviewStartResult> {
    try {
      // Stop preview first
      await this.stopPreview(workspaceId);
      
      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Start preview again
      return await this.startPreview(workspaceId);
    } catch (error: any) {
      console.error('❌ Error restarting preview:', error);
      return {
        success: false,
        message: 'Failed to restart preview server',
        error: error.message
      };
    }
  }

  /**
   * Auto-detect and start appropriate development server
   */
  async autoStartPreview(workspaceId: string): Promise<PreviewStartResult> {
    try {
      const workspace = await Workspace.findById(workspaceId);
      if (!workspace || !workspace.publicIp) {
        return {
          success: false,
          message: 'Workspace not available'
        };
      }

      const containerUrl = `http://${workspace.publicIp}:${workspace.containerConfig.port}`;
      
      // Get project structure to detect project type
      const response = await axios.get(`${containerUrl}/`, {
        timeout: getAppConfig().workspaceHealthCheckTimeoutMs
      });

      let startCommand = 'python3 -m http.server 8080'; // Default fallback

      if (response.data && response.data.files) {
        const files = response.data.files;
        
        // Detect project type based on files
        if (this.hasFile(files, 'package.json')) {
          // Node.js project
          if (this.hasFile(files, 'vite.config.js') || this.hasFile(files, 'vite.config.ts')) {
            startCommand = 'npm run dev || npx vite --host 0.0.0.0 --port 8080';
          } else if (this.hasFile(files, 'next.config.js')) {
            startCommand = 'npm run dev || npx next dev -H 0.0.0.0 -p 8080';
          } else {
            startCommand = 'npm start || npm run dev || npx serve -s . -l 8080';
          }
        } else if (this.hasFile(files, 'index.html')) {
          // Static HTML project
          startCommand = 'python3 -m http.server 8080';
        } else if (this.hasFile(files, 'requirements.txt') || this.hasFile(files, 'app.py')) {
          // Python project
          startCommand = 'python3 -m flask run --host=0.0.0.0 --port=8080 || python3 -m http.server 8080';
        }
      }

      // Start preview with detected command
      const startResponse = await axios.post(`${containerUrl}/api/preview/start`, {
        port: 8080,
        command: startCommand
      }, {
        timeout: getAppConfig().previewStartTimeoutMs
      });

      if (startResponse.data && startResponse.data.success) {
        const effectiveHost = workspace.previewHostname || workspace.publicIp;
        const previewUrl = `http://${effectiveHost}:8080`;

        // Save preview URL
        workspace.previewUrl = previewUrl;
        await workspace.save();

        // Update container record if exists
        const container = await ECSContainer.findOne({ workspaceId });
        if (container) {
          container.previewUrl = previewUrl;
          await container.addEvent('started', 'Preview server started (auto)');
        }

        return {
          success: true,
          previewUrl,
          message: 'Preview server started successfully'
        };
      }

      return {
        success: false,
        message: 'Failed to start preview server'
      };

    } catch (error: any) {
      return {
        success: false,
        message: 'Failed to start preview server',
        error: error.message
      };
    }
  }

  /**
   * Helper to check if a file exists in the file list
   */
  private hasFile(files: any[], filename: string): boolean {
    const checkFiles = (fileList: any[]): boolean => {
      for (const file of fileList) {
        if (file.name === filename) {
          return true;
        }
        if (file.type === 'directory' && file.children) {
          if (checkFiles(file.children)) {
            return true;
          }
        }
      }
      return false;
    };
    
    return checkFiles(files);
  }
}
