import express from 'express';
import {
  createECSWorkspace,
  getECSWorkspaceStructure,
  readECSWorkspaceFile,
  writeECSWorkspaceFile,
  deleteECSWorkspaceFile,
  saveECSWorkspaceFiles,
  extractToECSWorkspace,
  startECSPreview,
  stopECSPreview,
  getECSWorkspaceStatus,
  getECSPreviewStatus,
  getECSBackendStatus,
  startECSPublish
} from '../controllers/ecsWorkspaceController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// POST /api/ecs-workspace - Create a new ECS workspace
router.post('/', authenticateToken, createECSWorkspace);

// GET /api/ecs-workspace/:workspaceName/structure - Get workspace file structure
router.get('/:workspaceName/structure', authenticateToken, getECSWorkspaceStructure);

// POST /api/ecs-workspace/:workspaceName/files/read - Read a specific file
router.post('/:workspaceName/files/read', authenticateToken, readECSWorkspaceFile);

// POST /api/ecs-workspace/:workspaceName/files/write - Write/update a file
router.post('/:workspaceName/files/write', authenticateToken, writeECSWorkspaceFile);

// DELETE /api/ecs-workspace/:workspaceName/files - Delete a file
router.delete('/:workspaceName/files', authenticateToken, deleteECSWorkspaceFile);

// POST /api/ecs-workspace/:workspaceName/save - Save multiple files to ECS workspace
router.post('/:workspaceName/save', authenticateToken, saveECSWorkspaceFiles);

// POST /api/ecs-workspace/:workspaceName/extract - Extract files from LLM response and save to workspace
router.post('/:workspaceName/extract', authenticateToken, extractToECSWorkspace);

// POST /api/ecs-workspace/:workspaceName/preview - Start preview server
router.post('/:workspaceName/preview', authenticateToken, startECSPreview);

// DELETE /api/ecs-workspace/:workspaceName/preview - Stop preview server
router.delete('/:workspaceName/preview', authenticateToken, stopECSPreview);

// POST /api/ecs-workspace/:workspaceName/publish - Start publish (ci->build->preview)
router.post('/:workspaceName/publish', authenticateToken, startECSPublish);

// GET /api/ecs-workspace/:workspaceName/status - Get workspace container status
router.get('/:workspaceName/status', authenticateToken, getECSWorkspaceStatus);

// GET /api/ecs-workspace/:workspaceName/preview/status - Get preview status
router.get('/:workspaceName/preview/status', authenticateToken, getECSPreviewStatus);

// GET /api/ecs-workspace/:workspaceName/backend/status - Get backend status
router.get('/:workspaceName/backend/status', authenticateToken, getECSBackendStatus);

export default router;
