import { Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { ECSWorkspaceService } from '../services/ecsWorkspaceService';
import { ECSFileSystemService } from '../services/ecsFileSystemService';
import { ECSPreviewService } from '../services/ecsPreviewService';
import { CodeExtractor } from '../utils/codeExtractor';
import fetch from 'node-fetch';
import { ECSPublishService } from '../services/ecsPublishService';
import { getAWSConfig } from '../config/aws';
import { getAppConfig } from '../config/app';

// Helper function to get default lifetime from config
const getDefaultLifetime = () => {
  const config = getAWSConfig();
  return config.defaultLifetimeHours;
};

// Helper function to determine if a path should be excluded from counting
function shouldSkipPathForCount(filePath: string): boolean {
  const skipPatterns = [
    // Dependencies and package managers
    'node_modules',
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',

    // Version control
    '.git',
    '.gitignore',
    '.gitattributes',
    '.svn',
    '.hg',

    // Build outputs and caches
    'dist',
    'build',
    'out',
    '.next',
    '.nuxt',
    '.cache',
    '.parcel-cache',
    '.webpack',
    '.rollup.cache',
    'coverage',
    '.nyc_output',
    '.pytest_cache',
    '__pycache__',
    'target',
    'bin',
    'obj',

    // Logs and temporary files
    'logs',
    '*.log',
    '*.tmp',
    '*.temp',
    '.DS_Store',

    // IDE and editor files
    '.vscode',
    '.idea'
  ];

  // Always ignore baseline file created by container on init
  if (filePath === 'README.md') return true;

  return skipPatterns.some(pattern => {
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      return regex.test(filePath);
    }
    return filePath.includes(pattern);
  });
}

// Helper function to recursively count files in workspace structure with exclusions
function countFilesInStructure(structure: any): number {
  let count = 0;

  if (!structure || typeof structure !== 'object') {
    return 0;
  }

  for (const [, value] of Object.entries(structure)) {
    if (value && typeof value === 'object') {
      const type = (value as any).type;
      const children = (value as any).children;
      const path = (value as any).path || '';

      if (type === 'file') {
        if (!shouldSkipPathForCount(path)) {
          count++;
        }
      } else if (type === 'folder' && children) {
        // Handle children array format
        if (Array.isArray(children)) {
          const childrenObj: any = {};
          for (const child of children) {
            childrenObj[child.name] = child;
          }
          count += countFilesInStructure(childrenObj);
        } else {
          count += countFilesInStructure(children);
        }
      }
    }
  }

  return count;
}

// Lazy-load services to avoid initialization errors
let ecsWorkspaceService: ECSWorkspaceService | null = null;
let ecsFileSystemService: ECSFileSystemService | null = null;
let ecsPreviewService: ECSPreviewService | null = null;

const getECSWorkspaceService = () => {
  if (!ecsWorkspaceService) {
    ecsWorkspaceService = new ECSWorkspaceService();
  }
  return ecsWorkspaceService;
};

const getECSFileSystemService = () => {
  if (!ecsFileSystemService) {
    ecsFileSystemService = new ECSFileSystemService();
  }
  return ecsFileSystemService;
};

const getECSPreviewService = () => {
  if (!ecsPreviewService) {
    ecsPreviewService = new ECSPreviewService();
  }
  return ecsPreviewService;
};

// Lightweight in-memory cache for status checks to reduce repeated health probes
const STATUS_CACHE_TTL_MS = getAppConfig().workspaceStatusCacheTtlMs; // Configurable cache TTL
const statusCache: Map<string, { data: any; expiresAt: number }> = new Map();

// Create a new ECS workspace
export const createECSWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName, lifetimeHours } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    // Extract interview UUID from workspace name (format: workspace_${interviewUuid})
    const interviewUuid = workspaceName?.replace('workspace_', '') || '';
    
    if (!interviewUuid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid workspace name format'
      });
    }

    const workspaceInfo = await getECSWorkspaceService().createWorkspace({
      userId,
      userEmail: req.user!.email,
      interviewUuid,
      lifetimeHours: lifetimeHours || getDefaultLifetime()
    });

    res.json({
      success: true,
      data: {
        workspaceName,
        workspaceId: workspaceInfo.workspaceId,
        containerId: workspaceInfo.containerId,
        status: workspaceInfo.status,
        previewUrl: workspaceInfo.previewUrl,
        expiresAt: workspaceInfo.expiresAt
      },
      message: 'ECS workspace created successfully'
    });

  } catch (error: any) {
    console.error('Error creating ECS workspace:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating workspace',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get ECS workspace structure
export const getECSWorkspaceStructure = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    // Get or create workspace with validation and restoration
    const workspaceInfo = await getECSWorkspaceService().getOrCreateWorkspace(workspaceName, userId, req.user!.email, getDefaultLifetime());

    if (!workspaceInfo) {
      return res.status(404).json({
        success: false,
        message: 'Failed to get or create workspace'
      });
    }

    // If workspace is still creating, return status without trying to get structure
    if (workspaceInfo.status === 'creating' || workspaceInfo.status === 'starting') {
      return res.json({
        success: true,
        data: {
          structure: {},
          workspaceInfo
        },
        message: 'Workspace is being created, structure will be available once ready'
      });
    }

    const structure = await getECSFileSystemService().getWorkspaceStructure(workspaceInfo.workspaceId);

    res.json({
      success: true,
      data: {
        structure,
        workspaceInfo
      },
      message: 'Workspace structure retrieved successfully'
    });

  } catch (error: any) {
    console.error('Error getting ECS workspace structure:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving workspace structure',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Read file from ECS workspace
export const readECSWorkspaceFile = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const { filePath } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    const workspaceInfo = await getECSWorkspaceService().getOrCreateWorkspace(workspaceName, userId, req.user!.email, getDefaultLifetime());

    if (!workspaceInfo) {
      return res.status(404).json({
        success: false,
        message: 'Failed to get or create workspace'
      });
    }

    const file = await getECSFileSystemService().readFile(workspaceInfo.workspaceId, filePath);

    res.json({
      success: true,
      data: file,
      message: 'File read successfully'
    });

  } catch (error: any) {
    console.error('Error reading ECS workspace file:', error);
    res.status(500).json({
      success: false,
      message: 'Error reading file',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Write file to ECS workspace
export const writeECSWorkspaceFile = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const { filePath, content } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    const workspaceInfo = await getECSWorkspaceService().getOrCreateWorkspace(workspaceName, userId, req.user!.email, getDefaultLifetime());

    if (!workspaceInfo) {
      return res.status(404).json({
        success: false,
        message: 'Failed to get or create workspace'
      });
    }

    const result = await getECSFileSystemService().writeFile(workspaceInfo.workspaceId, filePath, content);

    if (result.success) {
      res.json({
        success: true,
        data: result.data,
        message: result.message
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.message,
        error: result.error
      });
    }

  } catch (error: any) {
    console.error('Error writing ECS workspace file:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving file',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Delete file from ECS workspace
export const deleteECSWorkspaceFile = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const { filePath } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    const workspaceInfo = await getECSWorkspaceService().getOrCreateWorkspace(workspaceName, userId, req.user!.email, getDefaultLifetime());

    if (!workspaceInfo) {
      return res.status(404).json({
        success: false,
        message: 'Failed to get or create workspace'
      });
    }

    const result = await getECSFileSystemService().deleteFile(workspaceInfo.workspaceId, filePath);

    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.message,
        error: result.error
      });
    }

  } catch (error: any) {
    console.error('Error deleting ECS workspace file:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting file',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Save multiple files to ECS workspace
export const saveECSWorkspaceFiles = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const { files } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    const workspaceInfo = await getECSWorkspaceService().getWorkspaceInfoByName(workspaceName, userId, req.user!.email);

    if (!workspaceInfo) {
      return res.status(404).json({
        success: false,
        message: 'Workspace not found'
      });
    }

    const result = await getECSFileSystemService().saveFiles(workspaceInfo.workspaceId, files);

    if (result.success) {
      res.json({
        success: true,
        data: result.data,
        message: result.message
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.message,
        error: result.error
      });
    }

  } catch (error: any) {
    console.error('Error saving ECS workspace files:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving files',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Extract files from LLM response and save to ECS workspace
export const extractToECSWorkspace = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const { llmResponse } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    // Extract files from LLM response
    const extractedContent = CodeExtractor.extractFullContent(llmResponse);
    const files: { [key: string]: string } = {};

    // Convert code blocks to files
    for (const block of extractedContent.codeBlocks) {
      if (block.filename) {
        files[block.filename] = block.content;
      }
    }

    if (Object.keys(files).length === 0) {
      return res.json({
        success: true,
        data: {
          workspaceName,
          files: {},
          savedFiles: [],
          count: 0
        },
        message: 'No files found in LLM response'
      });
    }

    // Use getOrCreateWorkspace to prevent duplicate workspace creation
    const workspaceInfo = await getECSWorkspaceService().getOrCreateWorkspace(workspaceName, userId, req.user!.email, getDefaultLifetime());

    if (!workspaceInfo) {
      return res.status(500).json({
        success: false,
        message: 'Failed to get or create workspace'
      });
    }

    const result = await getECSFileSystemService().saveFiles(workspaceInfo.workspaceId, files);

    if (result.success) {
      res.json({
        success: true,
        data: {
          workspaceName,
          workspaceId: workspaceInfo.workspaceId,
          files,
          savedFiles: Object.keys(files),
          count: Object.keys(files).length
        },
        message: result.message
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.message,
        error: result.error
      });
    }

  } catch (error: any) {
    console.error('Error extracting to ECS workspace:', error);
    res.status(500).json({
      success: false,
      message: 'Error extracting files to workspace',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Start preview server for ECS workspace
export const startECSPreview = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    const workspaceInfo = await getECSWorkspaceService().getWorkspaceInfoByName(workspaceName, userId, req.user!.email);

    if (!workspaceInfo) {
      return res.status(404).json({
        success: false,
        message: 'Workspace not found'
      });
    }

    const result = await getECSPreviewService().startPreview(workspaceInfo.workspaceId);

    if (result.success) {
      res.json({
        success: true,
        previewUrl: result.previewUrl,
        message: result.message
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.message,
        error: result.error
      });
    }

  } catch (error: any) {
    console.error('Error starting ECS preview:', error);
    res.status(500).json({
      success: false,
      message: 'Error starting preview server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Stop preview server for ECS workspace
export const stopECSPreview = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    const workspaceInfo = await getECSWorkspaceService().getWorkspaceInfoByName(workspaceName, userId, req.user!.email);

    if (!workspaceInfo) {
      return res.status(404).json({
        success: false,
        message: 'Workspace not found'
      });
    }

    const result = await getECSPreviewService().stopPreview(workspaceInfo.workspaceId);

    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.message,
        error: result.error
      });
    }

  } catch (error: any) {
    console.error('Error stopping ECS preview:', error);
    res.status(500).json({
      success: false,
      message: 'Error stopping preview server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Start publish server for ECS workspace
export const startECSPublish = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({ success: false, message: 'User authentication required' });
    }

    const force = (req.query.force === 'true') || (req.body && typeof req.body.force === 'boolean' ? req.body.force : false);

    // Use dedicated publish service to create a new ECS container and publish
    const publishService = new ECSPublishService();
    const result = await publishService.startPublishFromWorkspaceName(workspaceName, userId, req.user!.email, { force });

    if (result.success) {
      res.json({ success: true, liveUrl: result.liveUrl, hostname: result.hostname, publicIp: result.publicIp, message: result.message, alreadyRunning: result.alreadyRunning === true });
    } else {
      res.status(500).json({ success: false, message: result.message, error: result.error });
    }
  } catch (error: any) {
    res.status(500).json({ success: false, message: 'Error starting publish', error: process.env.NODE_ENV === 'development' ? error.message : undefined });
  }
};

// Get ECS workspace container status
export const getECSWorkspaceStatus = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    const workspaceInfo = await getECSWorkspaceService().getWorkspaceInfoByName(workspaceName, userId, req.user!.email);

    if (!workspaceInfo) {
      return res.status(404).json({
        success: false,
        message: 'Workspace not found',
        data: {
          containerStatus: 'failed', // Use valid ECSContainerStatus
          publicIp: null,
          isReady: false,
          hasFiles: false,
          fileCount: 0
        }
      });
    }

    // Serve from cache if fresh
    const cached = statusCache.get(workspaceInfo.workspaceId);
    if (cached && cached.expiresAt > Date.now()) {
      return res.json({ success: true, data: cached.data, message: `Container status: ${cached.data.containerStatus}${cached.data.isReady ? ` (ready with ${cached.data.fileCount} files)` : ' (not ready)'} [cached]` });
    }

    // FIXED: Actually verify the container is running and has files
    let actualStatus = workspaceInfo.status;
    let isReady = false;
    let hasFiles = false;
    let fileCount = 0;

    // Step 1: If database says running, verify it's actually running
    if (workspaceInfo.status === 'running' && workspaceInfo.publicIp) {
      try {
        // Get the workspace from database to verify it's actually running
        const { ECSWorkspaceService } = await import('../services/ecsWorkspaceService');
        const ecsWorkspaceService = new ECSWorkspaceService();
        const workspaceFromDb = await ecsWorkspaceService.getWorkspaceById(workspaceInfo.workspaceId);
        
        if (workspaceFromDb) {
          // FIXED: Properly verify container accessibility via public IP
          try {
            console.log(`🔍 ECS Status Check: Testing container accessibility at ${workspaceInfo.publicIp}:3000`);
            
            const containerUrl = `http://${workspaceInfo.publicIp}:3000`;
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // Increased timeout

            const response = await fetch(`${containerUrl}/health`, {
              method: 'GET',
              signal: controller.signal,
              headers: {
                'Accept': 'application/json',
                'User-Agent': 'ECS-Status-Check'
              }
            });

            clearTimeout(timeoutId);

            if (response.ok) {
              console.log(`✅ ECS Status Check: Container is accessible at ${workspaceInfo.publicIp}:3000`);
              
              // Step 2: Container is accessible, check if files are available
              try {
                const structure = await getECSFileSystemService().getWorkspaceStructure(workspaceInfo.workspaceId);
                
                // FIXED: Recursively count all files in the structure, not just top-level entries
                fileCount = countFilesInStructure(structure || {});
                hasFiles = fileCount > 0;
                
                // Container is accessible; mark ready regardless of file availability
                isReady = true;
                
                console.log(`📊 ECS Status Check: Container accessible, found ${fileCount} files (recursive count), hasFiles: ${hasFiles}, isReady: ${isReady}`);
              } catch (fileError) {
                console.log('⚠️ ECS Status Check: Container accessible but cannot access files:', fileError);
                isReady = false;
                hasFiles = false;
              }
            } else {
              console.log(`❌ ECS Status Check: Container health check failed - HTTP ${response.status}`);
              actualStatus = 'failed';
              isReady = false;
            }
          } catch (healthError: any) {
            console.log(`❌ ECS Status Check: Container not reachable at ${workspaceInfo.publicIp}:3000 - ${healthError.message}`);
            actualStatus = 'failed';
            isReady = false;
          }
        } else {
          console.log('⚠️ ECS Status Check: Workspace not found in database');
          actualStatus = 'failed'; // Use valid ECSContainerStatus
          isReady = false;
        }
      } catch (verifyError) {
        console.log('⚠️ ECS Status Check: Error verifying container:', verifyError);
        actualStatus = 'failed'; // Use valid ECSContainerStatus
        isReady = false;
      }
    } else {
      // Container is not marked as running or has no IP
      isReady = false;
      console.log(`📊 ECS Status Check: Container not ready - status: ${workspaceInfo.status}, publicIp: ${workspaceInfo.publicIp || 'null'}`);
    }

    const payload = {
      workspaceId: workspaceInfo.workspaceId,
      containerStatus: actualStatus,
      publicIp: workspaceInfo.publicIp,
      previewUrl: workspaceInfo.previewUrl,
      isReady: isReady,
      hasFiles: hasFiles,
      fileCount: fileCount,
      expiresAt: workspaceInfo.expiresAt
    };

    // Update cache
    statusCache.set(workspaceInfo.workspaceId, { data: payload, expiresAt: Date.now() + STATUS_CACHE_TTL_MS });

    res.json({
      success: true,
      data: payload,
      message: `Container status: ${actualStatus}${isReady ? ` (ready with ${fileCount} files)` : ' (not ready)'}`
    });

  } catch (error: any) {
    console.error('Error getting ECS workspace status:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting workspace status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
      data: {
        containerStatus: 'failed', // Use valid ECSContainerStatus
        publicIp: null,
        isReady: false,
        hasFiles: false,
        fileCount: 0
      }
    });
  }
};

// Get preview status for ECS workspace
export const getECSPreviewStatus = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User authentication required'
      });
    }

    const workspaceInfo = await getECSWorkspaceService().getWorkspaceInfoByName(workspaceName, userId, req.user!.email);

    if (!workspaceInfo) {
      return res.status(404).json({
        success: false,
        message: 'Workspace not found'
      });
    }

    const previewInfo = await getECSPreviewService().getPreviewStatus(workspaceInfo.workspaceId);

    if (previewInfo) {
      res.json({
        success: true,
        data: previewInfo
      });
    } else {
      res.status(404).json({
        success: false,
        message: 'Preview status not available'
      });
    }

  } catch (error: any) {
    console.error('Error getting ECS preview status:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting preview status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get backend status for ECS workspace
export const getECSBackendStatus = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { workspaceName } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    const workspaceInfo = await getECSWorkspaceService().getWorkspaceInfoByName(workspaceName, userId);
    if (!workspaceInfo) {
      return res.status(404).json({
        success: false,
        message: 'Workspace not found'
      });
    }

    const backendInfo = await getECSPreviewService().checkBackendStatus(workspaceInfo.workspaceId);

    if (backendInfo.success) {
      res.json({
        success: true,
        data: backendInfo
      });
    } else {
      res.status(404).json({
        success: false,
        message: backendInfo.message || 'Backend status not available'
      });
    }

  } catch (error: any) {
    console.error('Error getting ECS backend status:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting backend status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
