const http = require('http');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const net = require('net');

const PORT = 3000;
const WORKSPACE_DIR = '/workspace';

// Store preview process and logs
let previewProcess = null;
let previewPort = 8080;
let previewStatus = 'stopped';
let previewLogs = [];

// Store backend process and logs
let backendProcess = null;
let backendPort = 8000;
let backendStatus = 'stopped';
let backendLogs = [];

// Add publish process state
let publishProcess = null;
let publishPort = 8080;
let publishStatus = 'stopped';
let publishLogs = [];

// Helper function to find frontend directory
function findFrontendDirectory() {
  // Check for common frontend directory names
  const possiblePaths = [
    path.join(WORKSPACE_DIR, 'frontend'),
    path.join(WORKSPACE_DIR, 'client'),
    path.join(WORKSPACE_DIR, 'web'),
    path.join(WORKSPACE_DIR, 'app'),
    WORKSPACE_DIR // Check root directory as well
  ];

  for (const dirPath of possiblePaths) {
    try {
      if (fs.existsSync(dirPath)) {
        const packageJsonPath = path.join(dirPath, 'package.json');
        if (fs.existsSync(packageJsonPath)) {
          const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
          // Check if it has dev script (common for frontend projects)
          if (packageJson.scripts && packageJson.scripts.dev) {
            console.log(`✅ Found frontend directory: ${dirPath}`);
            return dirPath;
          }
        }
      }
    } catch (error) {
      console.error(`Error checking directory ${dirPath}:`, error);
    }
  }

  return null;
}

// Helper function to find backend directory
function findBackendDirectory() {
  const possiblePaths = [
    path.join(WORKSPACE_DIR, 'backend'),
    path.join(WORKSPACE_DIR, 'server'),
    path.join(WORKSPACE_DIR, 'api'),
    path.join(WORKSPACE_DIR, 'service')
  ];

  for (const dirPath of possiblePaths) {
    try {
      if (fs.existsSync(dirPath)) {
        const packageJsonPath = path.join(dirPath, 'package.json');
        if (fs.existsSync(packageJsonPath)) {
          const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
          // Check if it has start script or common backend dependencies
          if (packageJson.scripts && (packageJson.scripts.start || packageJson.scripts.dev)) {
            const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
            // Look for common backend frameworks
            if (deps.express || deps.fastify || deps.koa || deps.hapi || deps['@nestjs/core']) {
              console.log(`✅ Found backend directory: ${dirPath}`);
              return dirPath;
            }
          }
        }

        // Also check for common backend files
        const commonBackendFiles = ['server.js', 'app.js', 'index.js', 'main.js'];
        for (const file of commonBackendFiles) {
          if (fs.existsSync(path.join(dirPath, file))) {
            console.log(`✅ Found backend directory (by file): ${dirPath}`);
            return dirPath;
          }
        }
      }
    } catch (error) {
      console.error(`Error checking backend directory ${dirPath}:`, error);
    }
  }

  return null;
}

// Helper function to determine backend start command
function getBackendStartCommand(backendPath) {
  try {
    const packageJsonPath = path.join(backendPath, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

      if (packageJson.scripts) {
        // Prefer dev script for development mode
        if (packageJson.scripts.dev) {
          return 'npm run dev';
        }
        // Fall back to start script
        if (packageJson.scripts.start) {
          return 'npm start';
        }
      }

      // Check main entry point
      const mainFile = packageJson.main || 'index.js';
      if (fs.existsSync(path.join(backendPath, mainFile))) {
        return `node ${mainFile}`;
      }
    }

    // Try common entry points
    const commonFiles = ['server.js', 'app.js', 'index.js', 'main.js'];
    for (const file of commonFiles) {
      if (fs.existsSync(path.join(backendPath, file))) {
        return `node ${file}`;
      }
    }

    return null;
  } catch (error) {
    console.error('Error determining backend start command:', error);
    return null;
  }
}

// Helper function to get available directories for debugging
function getAvailableDirectories() {
  try {
    const entries = fs.readdirSync(WORKSPACE_DIR);
    const directories = entries.filter(entry => {
      const fullPath = path.join(WORKSPACE_DIR, entry);
      return fs.statSync(fullPath).isDirectory();
    });
    return directories.join(', ');
  } catch (error) {
    return 'Unable to read workspace directory';
  }
}

// Helper function to check if port is listening
function checkPortListening(port) {
  return new Promise((resolve) => {
    const socket = new net.Socket();

    socket.setTimeout(1000);
    socket.on('connect', () => {
      socket.destroy();
      resolve(true);
    });

    socket.on('timeout', () => {
      socket.destroy();
      resolve(false);
    });

    socket.on('error', () => {
      resolve(false);
    });

    socket.connect(port, 'localhost');
  });
}

// Create a simple HTTP server with health endpoint
const server = http.createServer((req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const url = new URL(req.url, `http://localhost:${PORT}`);
  
  // Health check endpoint
  if (url.pathname === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      workspace: 'ready'
    }));
    return;
  }

  // Root endpoint - serve directory listing
  if (url.pathname === '/') {
    try {
      // Function to recursively build directory structure
      function buildDirectoryStructure(dirPath, relativePath = '') {
        const items = [];

        try {
          const entries = fs.readdirSync(dirPath);

          for (const entry of entries) {
            const fullPath = path.join(dirPath, entry);
            const stats = fs.statSync(fullPath);
            // Use forward slashes and avoid URL encoding
            const itemRelativePath = relativePath ? `${relativePath}/${entry}` : entry;

            if (stats.isDirectory()) {
              items.push({
                name: entry,
                path: itemRelativePath,
                type: 'directory',
                size: 0,
                modified: stats.mtime.toISOString(),
                children: buildDirectoryStructure(fullPath, itemRelativePath)
              });
            } else {
              items.push({
                name: entry,
                path: itemRelativePath,
                type: 'file',
                size: stats.size,
                modified: stats.mtime.toISOString()
              });
            }
          }
        } catch (error) {
          console.error(`Error reading directory ${dirPath}:`, error);
        }

        return items;
      }

      const fileStructure = buildDirectoryStructure(WORKSPACE_DIR);

      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        status: 'ok',
        workspace: WORKSPACE_DIR,
        files: fileStructure
      }));
    } catch (error) {
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        status: 'error',
        message: error.message
      }));
    }
    return;
  }

  // File operations endpoint
  if (url.pathname.startsWith('/files/')) {
    // Properly decode the URL path to handle paths like frontend%2Ftest1.js -> frontend/test1.js
    const encodedPath = url.pathname.replace('/files/', '');
    const decodedPath = decodeURIComponent(encodedPath);
    const filePath = path.join(WORKSPACE_DIR, decodedPath);
    
    // Security check - ensure file is within workspace
    if (!filePath.startsWith(WORKSPACE_DIR)) {
      res.writeHead(403, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Access denied' }));
      return;
    }

    // Handle different HTTP methods
    if (req.method === 'GET') {
      // Read file
      try {
        if (fs.existsSync(filePath)) {
          const stats = fs.statSync(filePath);
          
          if (stats.isFile()) {
            const content = fs.readFileSync(filePath, 'utf8');
            res.writeHead(200, { 'Content-Type': 'text/plain' });
            res.end(content);
          } else {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Not a file' }));
          }
        } else {
          res.writeHead(404, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ error: 'File not found' }));
        }
      } catch (error) {
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: error.message }));
      }
      return;
    }
    
    if (req.method === 'PUT' || req.method === 'POST') {
      // Write file
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });
      
      req.on('end', () => {
        try {
          // Ensure directory exists
          const dir = path.dirname(filePath);
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
          }
          
          // Write file content
          fs.writeFileSync(filePath, body, 'utf8');
          
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: true,
            message: 'File saved successfully',
            path: decodedPath
          }));
        } catch (error) {
          res.writeHead(500, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ error: error.message }));
        }
      });
      return;
    }
    
    if (req.method === 'DELETE') {
      // Delete file
      try {
        if (fs.existsSync(filePath)) {
          const stats = fs.statSync(filePath);
          
          if (stats.isFile()) {
            fs.unlinkSync(filePath);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ 
              success: true, 
              message: 'File deleted successfully' 
            }));
          } else {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Not a file' }));
          }
        } else {
          res.writeHead(404, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ error: 'File not found' }));
        }
      } catch (error) {
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: error.message }));
      }
      return;
    }
  }

  // Preview API endpoints
  if (url.pathname === '/api/preview/start') {
    if (req.method === 'POST') {
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });

      req.on('end', () => {
        try {
          const requestData = JSON.parse(body);
          const port = requestData.port || 8080;

          // Check if preview is already running
          if (previewProcess && previewStatus === 'running') {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
              success: true,
              message: 'Preview server is already running',
              port: previewPort,
              status: previewStatus
            }));
            return;
          }

          // Find frontend directory
          const frontendPath = findFrontendDirectory();
          if (!frontendPath) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
              success: false,
              message: 'No frontend directory found. Expected "frontend" folder with package.json containing a "dev" script. Available directories: ' + getAvailableDirectories()
            }));
            return;
          }

          console.log(`🚀 Starting preview server in ${frontendPath}`);
          previewPort = port;
          previewStatus = 'starting';

          // Start npm install first (include devDependencies)
          console.log('📦 Starting npm install with devDependencies...');
          const installProcess = spawn('npm', ['install', '--include=dev'], {
            cwd: frontendPath,
            stdio: 'pipe'
          });

          let installErrors = [];

          installProcess.stdout?.on('data', (data) => {
            const output = data.toString();
            console.log(`📦 npm install: ${output}`);
          });

          installProcess.stderr?.on('data', (data) => {
            const error = data.toString();
            console.error(`❌ npm install error: ${error}`);
            installErrors.push(error);
          });

          installProcess.on('close', (installCode) => {
            if (installCode !== 0) {
              console.error(`❌ npm install failed with code ${installCode}`);
              console.error('Install errors:', installErrors.join('\n'));
              previewStatus = 'error';
              res.writeHead(500, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({
                success: false,
                message: `npm install failed with code ${installCode}. Errors: ${installErrors.join('; ')}`,
                port: port,
                status: 'error'
              }));
              return;
            }

            // Skip vite verification - just try to start the dev server
            // If vite is missing, the dev server start will fail with a clear error
            console.log('✅ npm install completed, proceeding with dev server...');
            startDevServer();
          });

          // Function to set up process event handlers
          function setupProcessHandlers(process, response, port, serverStarted, timeout) {
            process.stdout?.on('data', (data) => {
              const output = data.toString();
              console.log(`📡 Preview server output: ${output}`);

              // Store logs (keep last 100 lines)
              previewLogs.push(`[STDOUT] ${new Date().toISOString()}: ${output.trim()}`);
              if (previewLogs.length > 100) {
                previewLogs = previewLogs.slice(-100);
              }

              // Enhanced detection patterns for various frameworks
              const readyPatterns = [
                // Vite patterns
                'Local:   http://localhost:' + port,
                'Local:   http://0.0.0.0:' + port,
                'ready in',

                // Create React App patterns
                'Local:            http://localhost:' + port,
                'compiled successfully',
                'webpack compiled',

                // General patterns
                'server running',
                'dev server running',
                'ready on',
                'listening on',
                'started server on',

                // Fallback patterns
                'localhost:' + port,
                '0.0.0.0:' + port,
                'http://localhost:' + port,
                'http://0.0.0.0:' + port
              ];

              const isReady = readyPatterns.some(pattern => {
                const match = output.toLowerCase().includes(pattern.toLowerCase());
                if (match) {
                  console.log(`🎯 Detected ready pattern: "${pattern}" in output: "${output.trim()}"`);
                }
                return match;
              });

              // Also check for specific port mentions
              const portMentioned = output.includes(':' + port) || output.includes('port ' + port);

              if ((isReady || portMentioned) && !serverStarted && !response.headersSent) {
                serverStarted = true;
                previewStatus = 'running';
                clearTimeout(timeout);
                console.log(`✅ Preview server detected as ready on port ${port}`);
                console.log(`🔍 Detection trigger: ${isReady ? 'ready pattern' : 'port mentioned'}`);

                response.writeHead(200, { 'Content-Type': 'application/json' });
                response.end(JSON.stringify({
                  success: true,
                  message: 'Preview server started successfully',
                  port: port,
                  status: 'running'
                }));
              }
            });

            process.stderr?.on('data', (data) => {
              const error = data.toString();
              console.error(`❌ Preview server error: ${error}`);

              // Store error logs
              previewLogs.push(`[STDERR] ${new Date().toISOString()}: ${error.trim()}`);
              if (previewLogs.length > 100) {
                previewLogs = previewLogs.slice(-100);
              }

              // Check for critical errors that should fail the start
              if (error.includes('EADDRINUSE') || error.includes('port already in use')) {
                if (!serverStarted && !response.headersSent) {
                  serverStarted = true;
                  previewStatus = 'error';
                  clearTimeout(timeout);
                  response.writeHead(500, { 'Content-Type': 'application/json' });
                  response.end(JSON.stringify({
                    success: false,
                    message: 'Port already in use',
                    port: port,
                    status: 'error'
                  }));
                }
              }
            });
          }

          // Function to start the dev server
          function startDevServer() {
            console.log('✅ npm install completed successfully');

            // Try npm run dev first, fallback to npx vite if that fails
            console.log(`🚀 Starting dev server in ${frontendPath} on port ${port}`);

            // Check if package.json has dev script, otherwise use npx vite directly
            let devCommand, devArgs;
            try {
              const packageJson = JSON.parse(fs.readFileSync(path.join(frontendPath, 'package.json'), 'utf8'));
              if (packageJson.scripts && packageJson.scripts.dev) {
                devCommand = 'npm';
                devArgs = ['run', 'dev'];
                console.log('📝 Using npm run dev from package.json');
              } else {
                devCommand = 'npx';
                devArgs = ['vite', '--host', '0.0.0.0', '--port', port.toString()];
                console.log('📝 Using npx vite directly');
              }
            } catch (error) {
              // Fallback to npx vite if package.json can't be read
              devCommand = 'npx';
              devArgs = ['vite', '--host', '0.0.0.0', '--port', port.toString()];
              console.log('📝 Fallback to npx vite');
            }

            previewProcess = spawn(devCommand, devArgs, {
              cwd: frontendPath,
              stdio: 'pipe',
              env: {
                ...process.env,
                PORT: port.toString(),
                HOST: '0.0.0.0',
                VITE_HOST: '0.0.0.0',
                VITE_PORT: port.toString()
              }
            });

            let serverStarted = false;
            let responseTimeout;

            // Set up response timeout (120 seconds)
            responseTimeout = setTimeout(() => {
              if (!serverStarted && !res.headersSent) {
                console.log('⏰ Server start timeout after 120 seconds - dev server may still be starting');
                serverStarted = true;
                previewStatus = 'timeout';

                // Check if process is still running
                if (previewProcess && !previewProcess.killed) {
                  res.writeHead(202, { 'Content-Type': 'application/json' });
                  res.end(JSON.stringify({
                    success: false,
                    message: 'Preview server is starting but taking longer than expected. Check status in a few moments.',
                    port: port,
                    status: 'timeout',
                    hint: 'Use /api/preview/status to check if server becomes ready'
                  }));
                } else {
                  res.writeHead(500, { 'Content-Type': 'application/json' });
                  res.end(JSON.stringify({
                    success: false,
                    message: 'Preview server failed to start within timeout period',
                    port: port,
                    status: 'error'
                  }));
                }
              }
            }, 120000);

            // Set up process event handlers
            setupProcessHandlers(previewProcess, res, port, serverStarted, responseTimeout);

            previewProcess.on('close', (code) => {
              console.log(`📡 Preview server process exited with code ${code}`);

              // Only handle if this process hasn't been manually stopped
              if (previewProcess !== null) {
                previewProcess = null;
                previewStatus = 'stopped';

                if (!serverStarted) {
                  serverStarted = true;
                  clearTimeout(responseTimeout);

                  // If npm run dev failed, try npx vite as fallback
                  if (devCommand === 'npm' && code !== 0) {
                    console.log('🔄 npm run dev failed, trying npx vite as fallback...');

                    // Try with npx vite directly
                    previewProcess = spawn('npx', ['vite', '--host', '0.0.0.0', '--port', port.toString()], {
                      cwd: frontendPath,
                      stdio: 'pipe',
                      env: {
                        ...process.env,
                        PORT: port.toString(),
                        HOST: '0.0.0.0'
                      }
                    });

                    // Set up the same event handlers for the fallback process
                    setupProcessHandlers(previewProcess, res, port, serverStarted, responseTimeout);
                    return;
                  }

                  if (!res.headersSent) {
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({
                      success: false,
                      message: `Preview server exited with code ${code}. Recent logs: ${previewLogs.slice(-2).join('; ')}`,
                      port: port,
                      status: 'error'
                    }));
                  }
                }
              }
            });

            previewProcess.on('error', (error) => {
              console.error(`❌ Failed to start preview server: ${error.message}`);
              previewStatus = 'error';

              if (!serverStarted && !res.headersSent) {
                serverStarted = true;
                clearTimeout(responseTimeout);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                  success: false,
                  message: `Failed to start preview server: ${error.message}`,
                  port: port,
                  status: 'error'
                }));
              }
            });
          } // End of startDevServer function

          // Function to start backend server
          function startBackendServer() {
            const backendPath = findBackendDirectory();
            if (!backendPath) {
              console.log('ℹ️ No backend directory found, skipping backend startup');
              return;
            }

            console.log(`🚀 Starting backend server in ${backendPath} on port ${backendPort}`);
            backendStatus = 'starting';

            // Install backend dependencies first
            const backendInstallProcess = spawn('npm', ['install'], {
              cwd: backendPath,
              stdio: 'pipe'
            });

            let backendInstallErrors = [];

            backendInstallProcess.stdout?.on('data', (data) => {
              const output = data.toString();
              console.log(`📦 Backend npm install: ${output}`);
            });

            backendInstallProcess.stderr?.on('data', (data) => {
              const error = data.toString();
              console.error(`❌ Backend npm install error: ${error}`);
              backendInstallErrors.push(error);
            });

            backendInstallProcess.on('close', (installCode) => {
              if (installCode !== 0) {
                console.error(`❌ Backend npm install failed with code ${installCode}`);
                backendStatus = 'error';
                return;
              }

              console.log('✅ Backend npm install completed, starting backend server...');

              // Get backend start command
              const startCommand = getBackendStartCommand(backendPath);
              if (!startCommand) {
                console.error('❌ Could not determine backend start command');
                backendStatus = 'error';
                return;
              }

              // Parse command and arguments
              const [cmd, ...args] = startCommand.split(' ');

              // Start backend server
              backendProcess = spawn(cmd, args, {
                cwd: backendPath,
                stdio: 'pipe',
                env: {
                  ...process.env,
                  PORT: backendPort.toString(),
                  NODE_ENV: 'development'
                }
              });

              // Handle backend process output
              backendProcess.stdout?.on('data', (data) => {
                const output = data.toString();
                console.log(`🔧 Backend server output: ${output}`);

                // Store logs
                backendLogs.push(`[STDOUT] ${new Date().toISOString()}: ${output.trim()}`);
                if (backendLogs.length > 100) {
                  backendLogs = backendLogs.slice(-100);
                }

                // Check if backend is ready
                const readyPatterns = [
                  `listening on port ${backendPort}`,
                  `server running on port ${backendPort}`,
                  `listening on :${backendPort}`,
                  `server started on port ${backendPort}`,
                  'server is running',
                  'app listening'
                ];

                const isReady = readyPatterns.some(pattern =>
                  output.toLowerCase().includes(pattern.toLowerCase())
                );

                if (isReady) {
                  backendStatus = 'running';
                  console.log(`✅ Backend server detected as ready on port ${backendPort}`);
                }
              });

              backendProcess.stderr?.on('data', (data) => {
                const error = data.toString();
                console.error(`❌ Backend server error: ${error}`);

                // Store error logs
                backendLogs.push(`[STDERR] ${new Date().toISOString()}: ${error.trim()}`);
                if (backendLogs.length > 100) {
                  backendLogs = backendLogs.slice(-100);
                }
              });

              backendProcess.on('close', (code) => {
                console.log(`🔧 Backend server process exited with code ${code}`);
                backendProcess = null;
                backendStatus = 'stopped';
              });

              backendProcess.on('error', (error) => {
                console.error(`❌ Failed to start backend server: ${error.message}`);
                backendStatus = 'error';
              });
            });
          }

          // Start backend server alongside frontend
          startBackendServer();

        } catch (error) {
          res.writeHead(500, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: false,
            message: 'Error starting preview server',
            error: error.message
          }));
        }
      });
      return;
    }
  }

  // Publish API endpoints
  if (url.pathname === '/api/publish/start') {
    if (req.method === 'POST') {
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });

      req.on('end', () => {
        try {
          const requestData = JSON.parse(body || '{}');
          const port = requestData.port || 8080;

          // If a publish process is already running
          if (publishProcess && publishStatus === 'running') {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
              success: true,
              message: 'Publish server is already running',
              port: publishPort,
              status: publishStatus
            }));
            return;
          }

          // Find frontend directory
          const frontendPath = findFrontendDirectory();
          if (!frontendPath) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
              success: false,
              message: 'No frontend directory found. Expected folder with package.json and build/preview scripts. Available directories: ' + getAvailableDirectories()
            }));
            return;
          }

          publishPort = port;
          publishStatus = 'starting';
          publishLogs = [];

          // Helpers to decide install strategy
          const hasLockfile = fs.existsSync(path.join(frontendPath, 'package-lock.json')) || fs.existsSync(path.join(frontendPath, 'npm-shrinkwrap.json'));

          // Step 1: npm ci if lockfile exists, else npm install (include dev)
          const runNpmCiIfLockfile = () => new Promise((resolve) => {
            if (!hasLockfile) {
              console.log('ℹ️ Skipping npm ci (no lockfile found)');
              return resolve(false);
            }
            console.log('📦 Starting npm ci...');
            const ciProc = spawn('npm', ['ci'], {
              cwd: frontendPath,
              stdio: 'pipe',
              env: {
                ...process.env,
                NODE_ENV: 'development',
                NPM_CONFIG_PRODUCTION: ''
              }
            });
            ciProc.stdout?.on('data', (data) => {
              const output = data.toString();
              console.log(`📦 npm ci: ${output}`);
              publishLogs.push(`[CI][STDOUT] ${new Date().toISOString()}: ${output.trim()}`);
              if (publishLogs.length > 200) publishLogs = publishLogs.slice(-200);
            });
            ciProc.stderr?.on('data', (data) => {
              const error = data.toString();
              console.error(`❌ npm ci error: ${error}`);
              publishLogs.push(`[CI][STDERR] ${new Date().toISOString()}: ${error.trim()}`);
              if (publishLogs.length > 200) publishLogs = publishLogs.slice(-200);
            });
            ciProc.on('close', (code) => resolve(code === 0));
            ciProc.on('error', () => resolve(false));
          });

          const ensureLocalBinOnPath = (envObj, dir) => {
            const binPath = path.join(dir, 'node_modules', '.bin');
            const currentPath = envObj.PATH || process.env.PATH || '';
            if (!currentPath.split(':').includes(binPath)) {
              envObj.PATH = `${binPath}:${currentPath}`;
            }
            return envObj;
          };

          const runNpmInstallAll = () => new Promise((resolve) => {
            console.log('📦 Running npm install (including devDependencies)...');
            const envVars = ensureLocalBinOnPath({
              ...process.env,
              NODE_ENV: 'development',
              NPM_CONFIG_PRODUCTION: ''
            }, frontendPath);
            const instProc = spawn('npm', ['install', '--include=dev'], {
              cwd: frontendPath,
              stdio: 'pipe',
              env: envVars
            });
            instProc.stdout?.on('data', (data) => {
              const output = data.toString();
              console.log(`📦 npm install: ${output}`);
              publishLogs.push(`[INSTALL][STDOUT] ${new Date().toISOString()}: ${output.trim()}`);
              if (publishLogs.length > 200) publishLogs = publishLogs.slice(-200);
            });
            instProc.stderr?.on('data', (data) => {
              const error = data.toString();
              console.error(`❌ npm install error: ${error}`);
              publishLogs.push(`[INSTALL][STDERR] ${new Date().toISOString()}: ${error.trim()}`);
              if (publishLogs.length > 200) publishLogs = publishLogs.slice(-200);
            });
            instProc.on('close', (code) => resolve(code === 0));
            instProc.on('error', () => resolve(false));
          });

          const runNpmBuild = () => new Promise((resolve) => {
            console.log('🛠️ Running npm run build...');
            const envVars = ensureLocalBinOnPath({
              ...process.env,
              NODE_ENV: 'development'
            }, frontendPath);
            const buildProc = spawn('npm', ['run', 'build'], {
              cwd: frontendPath,
              stdio: 'pipe',
              env: envVars
            });

            let completed = false;

            const attachLogging = (proc) => {
              proc.stdout?.on('data', (data) => {
                const output = data.toString();
                console.log(`🛠️ build: ${output}`);
                publishLogs.push(`[BUILD][STDOUT] ${new Date().toISOString()}: ${output.trim()}`);
                if (publishLogs.length > 200) publishLogs = publishLogs.slice(-200);
              });
              proc.stderr?.on('data', (data) => {
                const error = data.toString();
                console.error(`❌ build error: ${error}`);
                publishLogs.push(`[BUILD][STDERR] ${new Date().toISOString()}: ${error.trim()}`);
                if (publishLogs.length > 200) publishLogs = publishLogs.slice(-200);
              });
            };

            attachLogging(buildProc);

            buildProc.on('close', (code) => {
              if (code === 0) {
                completed = true;
                return resolve(true);
              }
              console.log('🔄 npm run build failed, trying npx vite build as fallback...');
              const viteProc = spawn('npx', ['vite', 'build'], {
                cwd: frontendPath,
                stdio: 'pipe',
                env: envVars
              });
              attachLogging(viteProc);
              viteProc.on('close', (viteCode) => resolve(viteCode === 0));
              viteProc.on('error', () => resolve(false));
            });

            buildProc.on('error', () => {
              if (completed) return;
              console.log('🔄 npm run build error, trying npx vite build as fallback...');
              const viteProc = spawn('npx', ['vite', 'build'], {
                cwd: frontendPath,
                stdio: 'pipe',
                env: envVars
              });
              attachLogging(viteProc);
              viteProc.on('close', (viteCode) => resolve(viteCode === 0));
              viteProc.on('error', () => resolve(false));
            });
          });

          const startPreviewProd = () => {
            console.log(`🚀 Starting npm run preview on port ${port}...`);

            // Prefer npm run preview; fallback to npx vite preview
            let cmd = 'npm';
            let args = ['run', 'preview', '--', '--host', '0.0.0.0', '--port', port.toString()];

            try {
              const packageJson = JSON.parse(fs.readFileSync(path.join(frontendPath, 'package.json'), 'utf8'));
              if (!packageJson.scripts || !packageJson.scripts.preview) {
                cmd = 'npx';
                args = ['vite', 'preview', '--host', '0.0.0.0', '--port', port.toString()];
              }
            } catch {
              cmd = 'npx';
              args = ['vite', 'preview', '--host', '0.0.0.0', '--port', port.toString()];
            }

            publishProcess = spawn(cmd, args, {
              cwd: frontendPath,
              stdio: 'pipe',
              env: ensureLocalBinOnPath({
                ...process.env,
                PORT: port.toString(),
                HOST: '0.0.0.0'
              }, frontendPath)
            });

            let serverStarted = false;
            const timeout = setTimeout(() => {
              if (!serverStarted && !res.headersSent) {
                publishStatus = 'timeout';
                res.writeHead(202, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                  success: false,
                  message: 'Publish server is starting but taking longer than expected. Check status shortly.',
                  port: port,
                  status: 'timeout'
                }));
              }
            }, 180000); // 3 minutes for prod

            const readyPatterns = [
              'Local:',
              'Network:',
              `http://localhost:${port}`,
              `http://0.0.0.0:${port}`,
              `:${port}`,
              'vite v',
              'Preview server running'
            ];

            const onStdout = (data) => {
              const output = data.toString();
              console.log(`📡 publish preview: ${output}`);
              publishLogs.push(`[PREVIEW][STDOUT] ${new Date().toISOString()}: ${output.trim()}`);
              if (publishLogs.length > 200) publishLogs = publishLogs.slice(-200);

              const isReady = readyPatterns.some(p => output.toLowerCase().includes(p.toLowerCase()));
              if (isReady && !serverStarted && !res.headersSent) {
                serverStarted = true;
                clearTimeout(timeout);
                publishStatus = 'running';
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                  success: true,
                  message: 'Publish server started successfully',
                  port: port,
                  status: 'running'
                }));
              }
            };

            const onStderr = (data) => {
              const error = data.toString();
              console.error(`❌ publish preview error: ${error}`);
              publishLogs.push(`[PREVIEW][STDERR] ${new Date().toISOString()}: ${error.trim()}`);
              if (publishLogs.length > 200) publishLogs = publishLogs.slice(-200);
            };

            publishProcess.stdout?.on('data', onStdout);
            publishProcess.stderr?.on('data', onStderr);

            publishProcess.on('close', (code) => {
              publishProcess = null;
              if (!serverStarted && !res.headersSent) {
                publishStatus = 'stopped';
                clearTimeout(timeout);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                  success: false,
                  message: `Publish preview exited with code ${code}`,
                  port: port,
                  status: 'error'
                }));
              }
            });

            publishProcess.on('error', (err) => {
              publishStatus = 'error';
              if (!res.headersSent) {
                clearTimeout(timeout);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                  success: false,
                  message: `Failed to start publish preview: ${err.message}`,
                  port: port,
                  status: 'error'
                }));
              }
            });
          };

          // Function to start backend for production
          const startBackendProd = () => {
            const backendPath = findBackendDirectory();
            if (!backendPath) {
              console.log('ℹ️ No backend directory found, skipping backend startup for publish');
              return;
            }

            console.log(`🚀 Starting backend server for production in ${backendPath} on port ${backendPort}`);

            // Install backend dependencies first
            const backendInstallProcess = spawn('npm', ['install', '--production'], {
              cwd: backendPath,
              stdio: 'pipe'
            });

            backendInstallProcess.on('close', (installCode) => {
              if (installCode !== 0) {
                console.error(`❌ Backend npm install failed with code ${installCode}`);
                return;
              }

              console.log('✅ Backend npm install completed for production');

              // Get backend start command (prefer start over dev for production)
              let startCommand = getBackendStartCommand(backendPath);
              if (startCommand === 'npm run dev') {
                // For production, prefer npm start if available
                try {
                  const packageJsonPath = path.join(backendPath, 'package.json');
                  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
                  if (packageJson.scripts && packageJson.scripts.start) {
                    startCommand = 'npm start';
                  }
                } catch (error) {
                  // Keep the original command
                }
              }

              if (!startCommand) {
                console.error('❌ Could not determine backend start command for production');
                return;
              }

              // Parse command and arguments
              const [cmd, ...args] = startCommand.split(' ');

              // Start backend server for production
              backendProcess = spawn(cmd, args, {
                cwd: backendPath,
                stdio: 'pipe',
                env: {
                  ...process.env,
                  PORT: backendPort.toString(),
                  NODE_ENV: 'production'
                }
              });

              // Handle backend process output
              backendProcess.stdout?.on('data', (data) => {
                const output = data.toString();
                console.log(`🔧 Backend production output: ${output}`);
                backendLogs.push(`[PROD][STDOUT] ${new Date().toISOString()}: ${output.trim()}`);
                if (backendLogs.length > 100) {
                  backendLogs = backendLogs.slice(-100);
                }
              });

              backendProcess.stderr?.on('data', (data) => {
                const error = data.toString();
                console.error(`❌ Backend production error: ${error}`);
                backendLogs.push(`[PROD][STDERR] ${new Date().toISOString()}: ${error.trim()}`);
                if (backendLogs.length > 100) {
                  backendLogs = backendLogs.slice(-100);
                }
              });

              backendProcess.on('close', (code) => {
                console.log(`🔧 Backend production process exited with code ${code}`);
                backendProcess = null;
                backendStatus = 'stopped';
              });

              backendProcess.on('error', (error) => {
                console.error(`❌ Failed to start backend production server: ${error.message}`);
                backendStatus = 'error';
              });

              backendStatus = 'running';
              console.log(`✅ Backend production server started on port ${backendPort}`);
            });
          };

          // Run steps sequentially
          (async () => {
            const ciOk = await runNpmCiIfLockfile();
            if (!ciOk) {
              const instOk = await runNpmInstallAll();
              if (!instOk) {
                publishStatus = 'error';
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: false, message: 'Dependency installation failed' }));
                return;
              }
            }

            const buildOk = await runNpmBuild();
            if (!buildOk) {
              publishStatus = 'error';
              res.writeHead(500, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ success: false, message: 'Build failed' }));
              return;
            }

            // Start both frontend and backend in production mode
            startPreviewProd();
            startBackendProd();
          })();
        } catch (error) {
          res.writeHead(500, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ success: false, message: 'Error starting publish', error: error.message }));
        }
      });
      return;
    }
  }

  if (url.pathname === '/api/preview/stop') {
    if (req.method === 'POST') {
      try {
        let stoppedServices = [];

        // Stop frontend preview server
        if (previewProcess) {
          console.log('🛑 Stopping preview server...');

          // Remove event listeners to prevent race conditions
          previewProcess.removeAllListeners('close');
          previewProcess.removeAllListeners('error');

          // Kill the process
          previewProcess.kill('SIGTERM');

          // Wait a moment for graceful shutdown, then force kill if needed
          setTimeout(() => {
            if (previewProcess && !previewProcess.killed) {
              console.log('🔥 Force killing preview server...');
              previewProcess.kill('SIGKILL');
            }
          }, 5000);

          previewProcess = null;
          previewStatus = 'stopped';
          previewLogs = []; // Clear logs when stopping
          stoppedServices.push('frontend');
        }

        // Stop backend server
        if (backendProcess) {
          console.log('🛑 Stopping backend server...');

          // Remove event listeners to prevent race conditions
          backendProcess.removeAllListeners('close');
          backendProcess.removeAllListeners('error');

          // Kill the process
          backendProcess.kill('SIGTERM');

          // Wait a moment for graceful shutdown, then force kill if needed
          setTimeout(() => {
            if (backendProcess && !backendProcess.killed) {
              console.log('🔥 Force killing backend server...');
              backendProcess.kill('SIGKILL');
            }
          }, 5000);

          backendProcess = null;
          backendStatus = 'stopped';
          backendLogs = []; // Clear logs when stopping
          stoppedServices.push('backend');
        }

        if (stoppedServices.length > 0) {
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: true,
            message: `Stopped services: ${stoppedServices.join(', ')}`
          }));
        } else {
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: true,
            message: 'No services were running'
          }));
        }
      } catch (error) {
        console.error('❌ Error stopping services:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: false,
          message: 'Error stopping services',
          error: error.message
        }));
      }
      return;
    }
  }

  if (url.pathname === '/api/preview/status') {
    if (req.method === 'GET') {
      // For timeout status, check if server actually became ready
      if (previewStatus === 'timeout' && previewProcess && !previewProcess.killed) {
        // Try to detect if server is now ready by checking recent logs
        const recentLogs = previewLogs.slice(-10);
        const readyPatterns = [
          'localhost', 'Local:', 'ready', 'server running', 'dev server running',
          'Local:   http', 'Network: http', 'compiled successfully', 'webpack compiled'
        ];

        const isNowReady = recentLogs.some(log =>
          readyPatterns.some(pattern => log.toLowerCase().includes(pattern.toLowerCase()))
        );

        if (isNowReady) {
          previewStatus = 'running';
        }
      }

      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: true,
        status: previewStatus,
        port: previewPort,
        isRunning: previewProcess !== null && (previewStatus === 'running' || previewStatus === 'timeout'),
        processAlive: previewProcess !== null && !previewProcess.killed,
        message: previewStatus === 'timeout' ? 'Server may still be starting - check again in a few seconds' : undefined
      }));
      return;
    }
  }

  if (url.pathname === '/api/preview/logs') {
    if (req.method === 'GET') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: true,
        logs: previewLogs
      }));
      return;
    }
  }

  if (url.pathname === '/api/preview/debug') {
    if (req.method === 'GET') {
      // Check if port is actually listening
      checkPortListening(previewPort).then(async (isListening) => {
        // Get npm and vite versions for debugging
        const getVersion = (command, args) => {
          return new Promise((resolve) => {
            const proc = spawn(command, args, { stdio: 'pipe' });
            let output = '';
            proc.stdout?.on('data', (data) => output += data.toString());
            proc.on('close', () => resolve(output.trim() || 'not found'));
            proc.on('error', () => resolve('error'));
          });
        };

        const npmVersion = await getVersion('npm', ['--version']);
        const nodeVersion = await getVersion('node', ['--version']);
        const viteVersion = await getVersion('npx', ['vite', '--version']);

        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: true,
          debug: {
            status: previewStatus,
            port: previewPort,
            portListening: isListening,
            processExists: previewProcess !== null,
            processKilled: previewProcess ? previewProcess.killed : null,
            processPid: previewProcess ? previewProcess.pid : null,
            logsCount: previewLogs.length,
            recentLogs: previewLogs.slice(-5),
            frontendPath: findFrontendDirectory(),
            availableDirectories: getAvailableDirectories(),
            versions: {
              node: nodeVersion,
              npm: npmVersion,
              vite: viteVersion
            }
          }
        }));
      });
      return;
    }
  }

  // Backend API endpoints
  if (url.pathname === '/api/backend/status') {
    if (req.method === 'GET') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: true,
        status: backendStatus,
        port: backendPort,
        isRunning: backendProcess !== null && (backendStatus === 'running' || backendStatus === 'starting'),
        processAlive: backendProcess !== null && !backendProcess.killed,
        backendPath: findBackendDirectory()
      }));
      return;
    }
  }

  if (url.pathname === '/api/backend/logs') {
    if (req.method === 'GET') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: true,
        logs: backendLogs
      }));
      return;
    }
  }

  if (url.pathname === '/api/backend/stop') {
    if (req.method === 'POST') {
      try {
        if (backendProcess) {
          console.log('🛑 Stopping backend server...');

          // Remove event listeners to prevent race conditions
          backendProcess.removeAllListeners('close');
          backendProcess.removeAllListeners('error');

          // Kill the process
          backendProcess.kill('SIGTERM');

          // Wait a moment for graceful shutdown, then force kill if needed
          setTimeout(() => {
            if (backendProcess && !backendProcess.killed) {
              console.log('🔥 Force killing backend server...');
              backendProcess.kill('SIGKILL');
            }
          }, 5000);

          backendProcess = null;
          backendStatus = 'stopped';
          backendLogs = []; // Clear logs when stopping

          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: true,
            message: 'Backend server stopped successfully'
          }));
        } else {
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: true,
            message: 'Backend server was not running'
          }));
        }
      } catch (error) {
        console.error('❌ Error stopping backend server:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: false,
          message: 'Error stopping backend server',
          error: error.message
        }));
      }
      return;
    }
  }

  if (url.pathname === '/api/publish/status') {
    if (req.method === 'GET') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: true,
        status: publishStatus,
        port: publishPort,
        isRunning: publishProcess !== null && (publishStatus === 'running' || publishStatus === 'timeout'),
        processAlive: publishProcess !== null && !publishProcess.killed
      }));
      return;
    }
  }

  if (url.pathname === '/api/publish/logs') {
    if (req.method === 'GET') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ success: true, logs: publishLogs }));
      return;
    }
  }

  if (url.pathname === '/api/publish/stop') {
    if (req.method === 'POST') {
      try {
        if (publishProcess) {
          publishProcess.removeAllListeners('close');
          publishProcess.removeAllListeners('error');
          publishProcess.kill('SIGTERM');
          setTimeout(() => {
            if (publishProcess && !publishProcess.killed) {
              publishProcess.kill('SIGKILL');
            }
          }, 5000);
          publishProcess = null;
          publishStatus = 'stopped';
          publishLogs = [];
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ success: true, message: 'Publish server stopped successfully' }));
        } else {
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ success: true, message: 'Publish server was not running' }));
        }
      } catch (error) {
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, message: 'Error stopping publish server', error: error.message }));
      }
      return;
    }
  }

  // 404 for other paths
  res.writeHead(404, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ error: 'Not found' }));
});

// Start the server
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Workspace server running on port ${PORT}`);
  console.log(`📁 Serving workspace directory: ${WORKSPACE_DIR}`);
  console.log(`💚 Health endpoint: http://localhost:${PORT}/health`);
  
  // Create initial workspace structure
  try {
    if (!fs.existsSync(path.join(WORKSPACE_DIR, 'logs'))) {
      fs.mkdirSync(path.join(WORKSPACE_DIR, 'logs'), { recursive: true });
    }
    
    // Create a welcome file
    const welcomeContent = `# Welcome to your Workspace!

This is your development workspace running in AWS ECS.

## Available endpoints:
- \`/health\` - Health check endpoint
- \`/\` - Workspace file listing
- \`/files/<path>\` - File content access

## Workspace directory: ${WORKSPACE_DIR}

Happy coding! 🚀
`;
    
    fs.writeFileSync(path.join(WORKSPACE_DIR, 'README.md'), welcomeContent);
    
    console.log('✅ Workspace initialized successfully');
  } catch (error) {
    console.error('❌ Error initializing workspace:', error);
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('📴 Received SIGTERM, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('📴 Received SIGINT, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
