# Multi-stage build for workspace container
FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    git \
    curl \
    bash \
    python3 \
    make \
    g++ \
    procps \
    lsof \
    && rm -rf /var/cache/apk/*

# Create workspace directory
WORKDIR /workspace

# Install global development tools
RUN npm install -g \
    create-react-app \
    @vue/cli \
    @angular/cli \
    webpack \
    webpack-cli \
    nodemon \
    ts-node \
    typescript

# Create non-root user for security
RUN addgroup -g 1001 -S workspace && \
    adduser -S workspace -u 1001 -G workspace

# Set up workspace directory permissions
RUN chown -R workspace:workspace /workspace
USER workspace

# Expose ports for workspace server, frontend, and backend services
EXPOSE 3000 8080 8000

# Copy workspace server script
COPY server.js /workspace/server.js

# Health check endpoint - check the dedicated health endpoint
HEALTHCHECK --interval=30s --timeout=5s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Default command - start Node.js workspace server
CMD ["sh", "-c", "echo 'Workspace container ready' && \
    mkdir -p /workspace/logs && \
    echo '{\"status\":\"healthy\",\"timestamp\":\"'$(date -Iseconds)'\"}' > /workspace/health.json && \
    node /workspace/server.js"]
